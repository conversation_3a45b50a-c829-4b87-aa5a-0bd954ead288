import { Component, ElementRef, <PERSON><PERSON><PERSON><PERSON>, On<PERSON><PERSON>roy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { PROJECT_UNIT_EXCEL_TEMPLATE } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { assignToSort, getAppName, getSystemTimeOffset, getSystemTimeZoneId, validateAllFormFields } from 'src/app/core/utils/common.util';
import { ExcelUploadedStatusComponent } from 'src/app/features/leads/excel-uploaded-status/excel-uploaded-status.component';
import { FetchCustomFormField } from 'src/app/reducers/custom-form/custom-form.action';
import { getCustomFormField } from 'src/app/reducers/custom-form/custom-form.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { FetchProjectUnitExcelUploadedList, ProjectUnitExcelUpload, UploadMappedColumns } from 'src/app/reducers/project/project.action';
import { getColumnHeadings } from 'src/app/reducers/project/project.reducer';
import { FetchAdminsAndReportees, FetchUsersListForReassignment } from 'src/app/reducers/teams/teams.actions';
import { getAdminsAndReportees, getUserBasicDetails, getUsersListForReassignment } from 'src/app/reducers/teams/teams.reducer';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
@Component({
  selector: 'bulk-upload',
  templateUrl: './bulk-upload.component.html',
})
export class BulkUploadComponent implements OnInit, OnDestroy {
  @ViewChild('validModal') validModal: any;
  @ViewChild('fileInput') fileInput: ElementRef<HTMLInputElement>;
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  excelTemplatePath: string = PROJECT_UNIT_EXCEL_TEMPLATE;
  // Dynamic field lists based on API response - completely replacing static constants
  dynamicProjectUnitData: any[] = [];
  dynamicUnitData: any[] = [];
  dynamicProjectUnitDataColumns: any[] = []; // Replaces ProjectUnitDataColumns
  availableFields: string[] = []; // Fields from API response

  // Field mapping for control names (maps API field names to form control names)
  fieldControlMapping: { [key: string]: string } = {
    'Name': 'name',
    'FurnishingStatus': 'furnishingStatus',
    'BHKType': 'bhkType',
    'NoOfBHK': 'noOfBhk',
    'Facing': 'facing',
    'UnitArea': 'unitArea',
    'CarpetArea': 'carpetArea',
    'CarpetAreaUnit': 'carpetAreaUnit',
    'BuiltupArea': 'builtupArea',
    'BuiltupAreaUnit': 'builtupAreaUnit',
    'SuperBuiltupArea': 'superBuiltupArea',
    'SuperBuiltupAreaUnit': 'superBuiltupAreaUnit',
    'AreaUnit': 'areaUnit',
    'PricePerUnit': 'pricePerUnit',
    'TotalPrice': 'totalPrice',
    'UnitType': 'unitType',
    'UnitSubType': 'unitSubType',
    'Currency': 'currency',
    'MaintenanceCost': 'maintenanceCost',
    'Balconies': 'numberOfBalconies',
    'BathRooms': 'numberOfBathrooms',
    'DrawingOrLivingRooms': 'numberOfDrawingOrLivingRooms',
    'BedRooms': 'numberOfBedrooms',
    'Utilities': 'numberOfUtilities',
    'Kitchens': 'numberOfKitchens',
    'MaximumOccupants': 'maximumOccupants'
  };

  // Generate control name for fields not in predefined mapping
  generateControlName(fieldName: string): string {
    return this.fieldControlMapping[fieldName] || fieldName.toLowerCase().replace(/\s+/g, '');
  }

  // Update form controls dynamically based on available fields
  updateFormControls() {
    if (!this.availableFields.length) return;

    // Add form controls for all available fields
    this.availableFields.forEach(fieldName => {
      const controlName = this.generateControlName(fieldName);

      // Only add if control doesn't already exist
      if (!this.projectUnitMappingForm.get(controlName)) {
        const isRequired = this.requiredFields.includes(fieldName);
        const validators = isRequired ? [Validators.required] : [];

        this.projectUnitMappingForm.addControl(controlName, this.fb.control(null, validators));
      }
    });
  }

  // Required fields (these will be shown as required in UI)
  requiredFields: string[] = ['Name', 'UnitArea', 'AreaUnit'];
  projectUnitMappingForm: FormGroup;
  isFileTypeSupported: boolean = true;
  currentStep: number = 1;
  isValidModal: boolean = false;
  projectId: number;
  selectedUserId: string[] = [];
  selectedFile: File;
  count: any;
  formKeys: Array<string> = [];
  allActiveUsers: any[] = [];
  canAssignToAny: boolean = false;
  activeUsers: any[];
  s3BucketKey: string;
  notUploadedLeadsExcelPath: string;
  excelSheets: any = {};
  sheetNames: Array<string> = [];
  selectedSheet: string;
  userData: any;
  getAppName = getAppName;

  constructor(
    public modalRef: BsModalRef,
    public modalService: BsModalService,
    private _store: Store<AppState>,
    private router: Router,
    private sharedDataService: ShareDataService,
    private fb: FormBuilder,
    private headerTitle: HeaderTitleService,
    public metaTitle: Title
  ) {
    this.metaTitle.setTitle('CRM | Import Project Units');
    this.headerTitle.setLangTitle('Import Project Units');
    // Initialize with basic required fields, will be updated dynamically
    this.projectUnitMappingForm = this.fb.group({
      name: [null, Validators.required],
      unitArea: [null, Validators.required],
      areaUnit: [null, Validators.required]
    });
  }

  ngOnInit() {
    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        if (permissions?.includes('Permissions.Users.AssignToAny')) {
          this.canAssignToAny = true;
          this._store.dispatch(new FetchUsersListForReassignment());
        } else {
          this._store.dispatch(new FetchAdminsAndReportees());
        }
      });
    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
      });
    this._store
      .select(getAdminsAndReportees)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.activeUsers = data?.filter((user: any) => user.isActive);
        this.activeUsers = this.activeUsers?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.activeUsers = assignToSort(this.activeUsers, '');
      });

    this._store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allActiveUsers = data?.filter((user: any) => user.isActive);
        this.allActiveUsers = this.allActiveUsers?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.allActiveUsers = assignToSort(this.allActiveUsers, '');
      });

    this.sharedDataService.projectId$.subscribe((data: any) => {
      this.projectId = data;
    });

    // Fetch available fields from API
    this._store.dispatch(new FetchCustomFormField());
    this._store
      .select(getCustomFormField)
      .pipe(takeUntil(this.stopper))
      .subscribe((fields: any) => {
        if (fields && Array.isArray(fields)) {
          this.availableFields = fields;
          this.updateFormControls(); // Add form controls for all available fields
          this.updateDynamicFieldLists(); // Create field lists for UI
        }
      });
    // this.navigateToProjects()
  }

  onFileSelection(file: File) {
    this.selectedFile = file;
    this.currentStep = this.currentStep < 2 ? this.currentStep + 1 : this.currentStep;
  }

  updateDynamicFieldLists() {
    if (!this.availableFields.length) return;

    // Filter fields that exist in Excel headers (if available)
    const filteredFields = this.availableFields.filter(fieldName =>
      this.formKeys.length === 0 || this.formKeys.includes(fieldName)
    );

    // Create dynamic field structures from API response
    let index = 1;
    this.dynamicUnitData = [];
    this.dynamicProjectUnitData = [];
    this.dynamicProjectUnitDataColumns = [];

    filteredFields.forEach(fieldName => {
      const controlName = this.generateControlName(fieldName);

      const fieldConfig = {
        index: index++,
        displayName: fieldName,
        controlName: controlName
      };

      const dataColumnConfig = {
        displayName: fieldName,
        value: controlName
      };

      // Add to appropriate list based on whether it's required
      if (this.requiredFields.includes(fieldName)) {
        this.dynamicUnitData.push(fieldConfig);
      } else {
        this.dynamicProjectUnitData.push(fieldConfig);
      }

      // Add to data columns (replaces ProjectUnitDataColumns)
      this.dynamicProjectUnitDataColumns.push(dataColumnConfig);
    });
  }

  onAutoMapChange() {
    // Use dynamic field lists created from API response
    const mappingColumns = this.dynamicProjectUnitData.length > 0 ? this.dynamicProjectUnitData : [];
    const mappingReqColumns = this.dynamicUnitData.length > 0 ? this.dynamicUnitData : [];
    const form = this.projectUnitMappingForm;

    // Auto-map required fields
    mappingReqColumns?.forEach(column => {
      if (column?.displayName && form?.controls[column?.controlName] && this.formKeys?.includes(column?.displayName)) {
        form.patchValue({
          [column?.controlName]: column?.displayName
        });
      }
    });

    // Auto-map optional fields
    mappingColumns?.forEach(column => {
      if (column?.displayName && form?.controls[column?.controlName] && this.formKeys?.includes(column?.displayName)) {
        form.patchValue({
          [column?.controlName]: column?.displayName
        });
      }
    });
  }

  uploadFile() {
    this._store.dispatch(new ProjectUnitExcelUpload(this.selectedFile));
    this._store.dispatch(new FetchCustomFormField())
    this._store.select(getColumnHeadings).subscribe((data: any) => {
      this.excelSheets = data?.multiSheetColumnNames;
      if (this.excelSheets) this.sheetNames = Object.keys(this.excelSheets);
      this.selectedSheet = this.sheetNames[0];
      this.formKeys = data?.columnNames;
      this.s3BucketKey = data?.s3BucketKey;
      if (this.formKeys?.length) {
        this.currentStep = 3;
        // Update dynamic field lists when Excel headers are available
        this.updateDynamicFieldLists();
      }
      this.resetForm();
      this.onAutoMapChange();
    });
  }

  isValidForm() {
    if (!this.selectedSheet || !this.projectUnitMappingForm.valid) {
      validateAllFormFields(this.projectUnitMappingForm);
      return;
    } else {
      this.isValidModal = true;
      this.modalRef = this.modalService.show(this.validModal, {
        class: 'modal-350 modal-dialog-centered ip-modal-unset',
        keyboard: false,
      });
    }
  }

  replaceFile() {
    this.fileInput.nativeElement.click();
  }

  openBulkUploadedStatusModal() {
    this.navigateToProjects();
    if (this.modalRef) {
      this.modalRef.hide();
    }
    let initialState: any = {
      fieldType: location?.href.includes('properties') ? 'property' : 'project',
    };
    this.modalRef = this.modalService.show(ExcelUploadedStatusComponent, {
      class: 'modal-1100 modal-dialog-centered h-100 tb-modal-unset',
      initialState
    });
  }

  onSheetSelection() {
    this.formKeys = this.excelSheets?.[this.selectedSheet];
    // Update dynamic field lists when sheet selection changes
    this.updateDynamicFieldLists();
  }

  navigateToProjects() {
    this.router.navigate([`projects/edit-project/unit-info/${this.projectId}`]);
  }

  confirmSheet(trackerInfoModal: TemplateRef<any>) {
    this.modalRef.hide();
    this.sendProjectMappingDetails(trackerInfoModal);
  }

  sendProjectMappingDetails(trackerInfoModal: TemplateRef<any>) {
    if (
      !this.selectedSheet ||
      !this.projectUnitMappingForm.controls['name'].value
    ) {
      return;
    }
    const payload: any = {
      s3BucketKey: this.s3BucketKey,
      fileName: this.selectedFile?.name,
      mappedColumnData: {},
      userIds: [],
      projectId: this.projectId,
      timeZoneId: this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
      baseUTcOffset: this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset()
    };

    // Use dynamic data columns created from API response
    const dataColumns = this.dynamicProjectUnitDataColumns;
    dataColumns.map((item: any) => {
      if (this.projectUnitMappingForm.value[item.value]) {
        payload.mappedColumnData[item.displayName] =
          this.projectUnitMappingForm.value[item.value];
      }
    });
    payload.sheetName = this.selectedSheet || this.sheetNames[0];

    this._store.dispatch(new UploadMappedColumns(payload));
    this._store.dispatch(new FetchProjectUnitExcelUploadedList(1, 10));
    this.modalRef = this.modalService.show(
      trackerInfoModal,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          ignoreBackdropClick: true,
          keyboard: false,
        }
      )
    );
  }

  resetForm() {
    if (this.projectUnitMappingForm)
      this.projectUnitMappingForm.reset();
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
