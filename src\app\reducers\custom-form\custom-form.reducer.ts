import { Action, createSelector } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import { CustomFormActionTypes, FetchCustomFormFieldExistSuccess, FetchCustomFormFieldSuccess, FetchCustomFormSuccess } from './custom-form.action';

export type CustomFormState = {
  customForm?: any;
  isCustomFormLoading: boolean;
  customFormFieldExist: boolean;
  customFormField: any;
  isCustomFormFieldLoading: boolean;

};
const initialState: CustomFormState = {
  customForm: [],
  isCustomFormLoading: true,
  customFormFieldExist: false,
  customFormField: [],
  isCustomFormFieldLoading: true,
};
export function customFormReducer(
  state: CustomFormState = initialState,
  action: Action
): CustomFormState {
  switch (action.type) {
    case CustomFormActionTypes.FETCH_CUSTOMFORM:
      return {
        ...state,
        isCustomFormLoading: true,
      };
    case CustomFormActionTypes.FETCH_CUSTOMFORM_SUCCESS:
      return {
        ...state,
        customForm: (action as FetchCustomFormSuccess).resp,
        isCustomFormLoading: false,
      };
    case CustomFormActionTypes.FETCH_CUSTOMFORM_FIELD_EXIST_SUCCESS:
      return {
        ...state,
        customFormFieldExist: (action as FetchCustomFormFieldExistSuccess).resp,
      };
    case CustomFormActionTypes.FETCH_CUSTOMFORM:
      return {
        ...state,
        customFormField: (action as FetchCustomFormFieldSuccess).resp,
        isCustomFormFieldLoading: true,
      };
    case CustomFormActionTypes.FETCH_CUSTOMFORM_SUCCESS:
      return {
        ...state,
        customFormField: (action as FetchCustomFormFieldExistSuccess).resp,
      };
    default:
      return state;
  }
}

export const selectFeature = (state: AppState) => state.customForm;

export const getCustomForm = createSelector(
  selectFeature,
  (state: CustomFormState) => state.customForm
);

export const getCustomFormIsLoading = createSelector(
  selectFeature,
  (state: CustomFormState) => state.isCustomFormLoading
);

export const getCustomFormField = createSelector(
  selectFeature,
  (state: CustomFormState) => state.customForm
);

export const getCustomFormFieldIsLoading = createSelector(
  selectFeature,
  (state: CustomFormState) => state.isCustomFormLoading
);

export const getCustomFormFieldExist = createSelector(
  selectFeature,
  (state: CustomFormState) => state.customFormFieldExist
);